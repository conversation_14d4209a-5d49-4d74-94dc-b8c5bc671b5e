package repository

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/model"
)

type parkingLotRepository struct {
	db *gorm.DB
}

func NewParkingLotRepository(db *gorm.DB) ParkingLotRepository {
	return &parkingLotRepository{db: db}
}

func (r *parkingLotRepository) Create(ctx context.Context, lot *domain.ParkingLot) error {
	if lot == nil {
		return fmt.Errorf("parking lot cannot be nil")
	}

	lotModel := &model.ParkingLotModel{}
	lotModel.FromDomain(lot)

	if err := r.db.WithContext(ctx).Create(lotModel).Error; err != nil {
		return fmt.Errorf("failed to create parking lot: %w", err)
	}

	*lot = *lotModel.ToDomain()
	return nil
}

func (r *parkingLotRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error) {
	var lotModel model.ParkingLotModel
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&lotModel).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("parking lot not found")
		}
		return nil, fmt.Errorf("failed to get parking lot: %w", err)
	}

	return lotModel.ToDomain(), nil
}

func (r *parkingLotRepository) List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error) {
	var lotModels []model.ParkingLotModel
	query := r.db.WithContext(ctx).Where("deleted_at IS NULL").Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&lotModels).Error; err != nil {
		return nil, fmt.Errorf("failed to list parking lots: %w", err)
	}

	lots := make([]*domain.ParkingLot, len(lotModels))
	for i, lotModel := range lotModels {
		lots[i] = lotModel.ToDomain()
	}

	return lots, nil
}

func (r *parkingLotRepository) SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error) {
	var lotModels []model.ParkingLotModel
	
	// Calculate bounding box for initial filtering
	radiusKm := float64(radius) / 1000.0
	latDelta := radiusKm / 111.0 // Approximate km per degree latitude
	lngDelta := radiusKm / (111.0 * math.Cos(lat*math.Pi/180.0)) // Approximate km per degree longitude

	query := r.db.WithContext(ctx).
		Where("deleted_at IS NULL").
		Where("latitude BETWEEN ? AND ?", lat-latDelta, lat+latDelta).
		Where("longitude BETWEEN ? AND ?", lng-lngDelta, lng+lngDelta)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&lotModels).Error; err != nil {
		return nil, fmt.Errorf("failed to search nearby parking lots: %w", err)
	}

	// Filter by exact distance and sort by distance
	var nearbyLots []*domain.ParkingLot
	for _, lotModel := range lotModels {
		distance := calculateDistance(lat, lng, lotModel.Latitude, lotModel.Longitude)
		if distance <= float64(radius) {
			nearbyLots = append(nearbyLots, lotModel.ToDomain())
		}
	}

	return nearbyLots, nil
}

func (r *parkingLotRepository) Update(ctx context.Context, lot *domain.ParkingLot) error {
	if lot == nil {
		return fmt.Errorf("parking lot cannot be nil")
	}

	lotModel := &model.ParkingLotModel{}
	lotModel.FromDomain(lot)
	lotModel.UpdatedAt = time.Now()

	if err := r.db.WithContext(ctx).Save(lotModel).Error; err != nil {
		return fmt.Errorf("failed to update parking lot: %w", err)
	}

	*lot = *lotModel.ToDomain()
	return nil
}

func (r *parkingLotRepository) Delete(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Model(&model.ParkingLotModel{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", time.Now())

	if result.Error != nil {
		return fmt.Errorf("failed to delete parking lot: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("parking lot not found")
	}

	return nil
}

// Helper function to calculate distance between two points using Haversine formula
func calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371000 // Earth radius in meters

	lat1Rad := lat1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	deltaLat := (lat2 - lat1) * math.Pi / 180
	deltaLng := (lng2 - lng1) * math.Pi / 180

	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return earthRadius * c
}

// Admin-specific methods for extended functionality
func (r *parkingLotRepository) ListWithFilters(ctx context.Context, status *domain.LotStatus, search *string, limit, offset int) ([]*domain.ParkingLot, int, error) {
	query := r.db.WithContext(ctx).Model(&model.ParkingLotModel{}).Where("deleted_at IS NULL")

	if status != nil {
		query = query.Where("status = ?", string(*status))
	}

	if search != nil && *search != "" {
		searchPattern := "%" + *search + "%"
		query = query.Where("name ILIKE ? OR address ILIKE ?", searchPattern, searchPattern)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count parking lots: %w", err)
	}

	// Get paginated results
	var lotModels []model.ParkingLotModel
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&lotModels).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list parking lots with filters: %w", err)
	}

	lots := make([]*domain.ParkingLot, len(lotModels))
	for i, lotModel := range lotModels {
		lots[i] = lotModel.ToDomain()
	}

	return lots, int(total), nil
}

func (r *parkingLotRepository) GetWithStats(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, map[string]interface{}, error) {
	lot, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, nil, err
	}

	// Get current occupancy (active sessions)
	var currentOccupancy int64
	if err := r.db.WithContext(ctx).Model(&model.SessionModel{}).
		Where("parking_lot_id = ? AND status = 'active'", id).
		Count(&currentOccupancy).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to get current occupancy: %w", err)
	}

	// Get today's revenue
	today := time.Now().Truncate(24 * time.Hour)
	var todayRevenue int64
	if err := r.db.WithContext(ctx).Model(&model.PaymentModel{}).
		Joins("JOIN sessions ON payments.session_id = sessions.id").
		Where("sessions.parking_lot_id = ? AND payments.status = 'completed' AND payments.created_at >= ?", id, today).
		Select("COALESCE(SUM(payments.amount), 0)").
		Scan(&todayRevenue).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to get today's revenue: %w", err)
	}

	stats := map[string]interface{}{
		"current_occupancy": currentOccupancy,
		"today_revenue":     todayRevenue,
		"occupancy_rate":    float64(currentOccupancy) / float64(lot.TotalSpots) * 100,
	}

	return lot, stats, nil
}

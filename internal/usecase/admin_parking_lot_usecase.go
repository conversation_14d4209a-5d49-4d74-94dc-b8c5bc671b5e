package usecase

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
	"github.com/smooth-inc/backend/internal/repository"
)

type adminParkingLotUsecase struct {
	parkingLotRepo repository.ParkingLotRepository
}

func NewAdminParkingLotUsecase(parkingLotRepo repository.ParkingLotRepository) AdminParkingLotUsecase {
	return &adminParkingLotUsecase{
		parkingLotRepo: parkingLotRepo,
	}
}

func (uc *adminParkingLotUsecase) Create(ctx context.Context, name, address string, latitude, longitude float64, totalSpots, hourlyRate int, features []string) (*domain.ParkingLot, error) {
	if name == "" {
		return nil, fmt.Errorf("name is required")
	}
	if address == "" {
		return nil, fmt.Errorf("address is required")
	}
	if totalSpots <= 0 {
		return nil, fmt.Errorf("total spots must be greater than 0")
	}
	if hourlyRate < 0 {
		return nil, fmt.Errorf("hourly rate cannot be negative")
	}

	lot, err := domain.NewParkingLot(name, address, latitude, longitude, totalSpots, hourlyRate)
	if err != nil {
		return nil, fmt.Errorf("failed to create parking lot domain: %w", err)
	}

	if features != nil {
		lot.Features = features
	}

	if err := uc.parkingLotRepo.Create(ctx, lot); err != nil {
		return nil, fmt.Errorf("failed to create parking lot: %w", err)
	}

	return lot, nil
}

func (uc *adminParkingLotUsecase) GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error) {
	lot, err := uc.parkingLotRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get parking lot: %w", err)
	}
	return lot, nil
}

func (uc *adminParkingLotUsecase) List(ctx context.Context, status *domain.LotStatus, search *string, limit, offset int) ([]*domain.ParkingLot, int, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// Use the extended repository method if available
	if extRepo, ok := uc.parkingLotRepo.(interface {
		ListWithFilters(ctx context.Context, status *domain.LotStatus, search *string, limit, offset int) ([]*domain.ParkingLot, int, error)
	}); ok {
		return extRepo.ListWithFilters(ctx, status, search, limit, offset)
	}

	// Fallback to basic list method
	lots, err := uc.parkingLotRepo.List(ctx, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list parking lots: %w", err)
	}

	// Apply filters manually if repository doesn't support them
	var filteredLots []*domain.ParkingLot
	for _, lot := range lots {
		if status != nil && lot.Status != *status {
			continue
		}
		if search != nil && *search != "" {
			// Simple search implementation
			searchTerm := *search
			if !contains(lot.Name, searchTerm) && !contains(lot.Address, searchTerm) {
				continue
			}
		}
		filteredLots = append(filteredLots, lot)
	}

	return filteredLots, len(filteredLots), nil
}

func (uc *adminParkingLotUsecase) Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*domain.ParkingLot, error) {
	lot, err := uc.parkingLotRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get parking lot: %w", err)
	}

	// Apply updates
	if name, ok := updates["name"].(string); ok && name != "" {
		lot.Name = name
	}
	if address, ok := updates["address"].(string); ok && address != "" {
		lot.Address = address
	}
	if latitude, ok := updates["latitude"].(float64); ok {
		lot.Latitude = latitude
	}
	if longitude, ok := updates["longitude"].(float64); ok {
		lot.Longitude = longitude
	}
	if totalSpots, ok := updates["total_spots"].(int); ok && totalSpots > 0 {
		lot.TotalSpots = totalSpots
	}
	if heightLimitCm, ok := updates["height_limit_cm"].(int); ok {
		lot.HeightLimitCm = &heightLimitCm
	}
	if hourlyRate, ok := updates["hourly_rate"].(int); ok && hourlyRate >= 0 {
		lot.HourlyRate = hourlyRate
	}
	if dailyMaxRate, ok := updates["daily_max_rate"].(int); ok {
		lot.DailyMaxRate = &dailyMaxRate
	}
	if freeMinutes, ok := updates["free_minutes"].(int); ok && freeMinutes >= 0 {
		lot.FreeMinutes = freeMinutes
	}
	if is24h, ok := updates["is_24h"].(bool); ok {
		lot.Is24h = is24h
	}
	if openTime, ok := updates["open_time"].(string); ok {
		lot.OpenTime = &openTime
	}
	if closeTime, ok := updates["close_time"].(string); ok {
		lot.CloseTime = &closeTime
	}
	if features, ok := updates["features"].([]string); ok {
		lot.Features = features
	}
	if status, ok := updates["status"].(string); ok {
		switch status {
		case "active":
			lot.SetActive()
		case "maintenance":
			lot.SetMaintenance()
		case "closed":
			lot.SetClosed()
		default:
			return nil, fmt.Errorf("invalid status: %s", status)
		}
	}
	if operatorName, ok := updates["operator_name"].(string); ok {
		lot.OperatorName = &operatorName
	}
	if contactPhone, ok := updates["contact_phone"].(string); ok {
		lot.ContactPhone = &contactPhone
	}

	if err := uc.parkingLotRepo.Update(ctx, lot); err != nil {
		return nil, fmt.Errorf("failed to update parking lot: %w", err)
	}

	return lot, nil
}

func (uc *adminParkingLotUsecase) Delete(ctx context.Context, id uuid.UUID) error {
	// Check if parking lot exists
	_, err := uc.parkingLotRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get parking lot: %w", err)
	}

	if err := uc.parkingLotRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete parking lot: %w", err)
	}

	return nil
}

func (uc *adminParkingLotUsecase) GetWithStats(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, map[string]interface{}, error) {
	// Use the extended repository method if available
	if extRepo, ok := uc.parkingLotRepo.(interface {
		GetWithStats(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, map[string]interface{}, error)
	}); ok {
		return extRepo.GetWithStats(ctx, id)
	}

	// Fallback to basic get method
	lot, err := uc.parkingLotRepo.GetByID(ctx, id)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get parking lot: %w", err)
	}

	// Return basic stats
	stats := map[string]interface{}{
		"current_occupancy": 0,
		"today_revenue":     0,
		"occupancy_rate":    0.0,
	}

	return lot, stats, nil
}

// Helper function for string search
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
				s[len(s)-len(substr):] == substr || 
				containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

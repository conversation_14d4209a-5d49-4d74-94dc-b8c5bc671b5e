// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9a28cyXbYXylMLmAKmdGQkmzs5ZeEIsW7ukvpEqS0NxuZJordNTO17OnuraomNVcm",
	"EAqG4cQxjMTIhyAwggBBEOQJBAjyIUDyYwb+H0a9uqu7q7qr5y3t/bJLTdfznFOnTp3np16QTNMkRjGj",
	"vcNPPYJomsQUiX+8hOEF+ilDlPF/BUnMUCz+hGka4QAynMTDH2kS899oMEFTyP/6BUGj3mHvHwyLoYfy",
	"Kx2+IiQhF2qS3sPDQ78XIhoQnPLBeod8TqAnfej3jpN4FOFggwvIZ3zo904TcoPDEMWbm76Y8qHfex0z",
	"RGIYXSJyh4jou7mV6MmBnB3I6R/6vbcJO02yONzcUt4mDMgpH/q99zHM2CQh+Hdog0sozco/q5584KOA",
	"4TvI0DnBAY7HnILw2Dg5KUlSRBiWpwqNRoh3QKckmfIfRgmZQtY77IWQoQHDU9Tr99gsRb3DHmUEx2O+",
	"7bzb+5jhyLffQ79H0E8ZJhxWHypzX+XNk5sfkST5o3CK43NIbnE8PkvYCWIQR2LdMIp+M+odfmgGZNG1",
	"99Cvbh3eIQLH6BJRipP4JCNQQveTXgeOGRojQWWBgKJqInpjhqa0DZHF/BINfCg1OCQEzvi/UwRvv00y",
	"Uh61BvFqN5YwGF2gOxRnyL5m0ULtjtqaPNQg7oGDy2w6hWRWpyQYhgRR++IDgiBD4RHzp7AgIwTF7DdB",
	"kKUwDmb2PeKwNGKW4dA2WAynyLowyiDLWtHIty0bCrCGcOYB+DRhnlCvwZjmZ78G5QhPMbNPG6kJvSjT",
	"gVULoSWjEUWsYacdNzmboniRkyz6WY5xiGmaMeS/9RPZQS/BsmWCRlkc+o94Ido3DEjlKfRCijqxBkYy",
	"iohX1/cUkbxfp9MtgOs+2tMkix0ksMDR9jy0aU6db13HN5ULf4PYJAntLSLI0NtseiNBWGcAEtqv/Zbk",
	"xy40OHOWwQdIkfr5tRAQXtuXy1HtuRbe1AGYttO3GH9pYgQKEZ35T4nuXLdcA+85clKmGwacIrrzH97L",
	"wn0iSNl7ikJ/6ico4Hs27mUvgKkOTiCdGxhYQBbYBJfhIHTymPXxEUyFSGwelJskiRAUwOyOwDaWIr6/",
	"E7960JRo6I+hV1OIo62wDr7YNTAOMWxHtmESUhem4dycfgN05QrFoayCY4TQS4LgbZjct177p2bb0sXn",
	"/74wmLDn9ZRD34tMN8IhKqLPuuWQUL3n3uBYC5D1cVHMyOwdlofF83H8EbNuPbw52TnEoZ2P+UhLbbKQ",
	"l3SjkZRLN2tmPJonrpz10K5XsEM8X579vE85daiRnaqaJuoPMQ3416OGNt3JciGCaNkl5w3uLWYsUezp",
	"VQxvIuQgduS8BcWX7xHBI+zq7FQGpJMktn8hSdTKJsXGeDtvwAk2WTpGccezwUcwLq3FgLkA51wK/p7M",
	"jgtmZ8kYx10WtgBuO0og+W1YUxpsgEbkQ/I4oyyZaqZb1xSu5DmglFjIxU0ycZo7Uc2iFL7KN8OXR7m7",
	"zHtWfyk3PPj5Ijte1iVBtHpkrTvL2MS9JxgEiNJ3yS2K7ST0McUE0ddxo5LkErEsvcjtIJ8qtp3fThCb",
	"IAJUa0B5c4ApyE0nfQtpEjQiiE7caxPDvM6NU80Xet60MEL5if8c4E7I6tvIQ8KpWEFhFGSc74ZAiblg",
	"hBDAMfj1+Q8FOMzXQN7jFKH6gKcIAd0EJzG4yZ9f/cqSbiBFaoj6LCMcw8jx1QaDMYoRgQxdoADhVG10",
	"BLOI9Q4ZyVDVzPcr1R7AOAQUxSEgsifgd/sUMhzAKJpZCYI3f5swPFImyNbJLvn4mug4aiMkYBObg9hm",
	"yig6kQOfV9WxjRO+pwhwmvoDClS7fPqpHKE+W8WCqAimiu+rNvpzHvCmByYk4UsCY/uly7+eQcpe2L8K",
	"Y5Y0YuUw6ZWI13jlxXcJDpoealNEKRyL9aOPkGOqd9hTmwMpSTiXQiGgmeBXo6xEI6b2HHe6OxV2Xvuq",
	"7w1i0OowqzYUp+w9iZZ4ki6hcFcwst33thP8Mkk4+7GIIjAOUCTZiZNfqEYdDaELCDlx2O2hN0kyEs0u",
	"lDJoYTPr4oor/S9f2uqktxKt/S08ZC2vZEU5ph250X6xsITttdEG2r7Mt4PibMq5rHB+IFMheeQkzP+W",
	"l4T4O06u6SS5N1hvsarjCYzH6BxSep+Q0Hn7K4u/bmcRA2QDkOoWNjEa3bsHeIvu885gb4pjPM2m4BsQ",
	"TCCBAUOEPun1e1Mcn6F4zCa9w2/aXFiqay4vwHYPHYvjrGDtdsvpeoIXO0LrOhQVKOm5Kss0By6Ylhto",
	"BdNwS5BNTihRQpHeRf1rEjMYsHOnniCEOJq9gR/dXHKEIMsI6ujIMyIINWqgJwiPJ+yMv6eOp44mbfyb",
	"Pnsxcdm/GGZZWEFtkt1EBl5jKYsIJ5N43KW981mbpCh2IoOjFLKEOFXZrS42JvmJNfRz2jD2bO6nNGgD",
	"EUZCgHfxsAhSWpK5p/Cj5ibPbTcvJnAMY1hpetBflV2RoHF9JQf7tlOOCIZRIXYa7V+0nXA1S78KAGOD",
	"lQkaIOzluCg98jSBlPZm21wqB73IItQuTpptaxy/mLcyqm1HJ5BObhJIwqMYRjOGA4ueNrlD5A6je5u+",
	"geE71Kynk23ea92IpUEHN8dEe9tpVuJxwtfiiMhhLkY8nkDCSgy1g1Gktgn+g5cs5HDgaltNsJHFsCQ1",
	"fAUblrOsdyRpwirtiNB25ZtNi+B+q1sfwSdWRQJQorTHi9iYejUvxhPEUMBP3Akm8g9TtBbW5Z60zVll",
	"57x7k1bSOuq1ejiq0a8JChISin8r1F0rsEjxXQ1jW0SLNq1RPQf2RgkBfAVAziBk7Do5Geh0Cbcm7yqv",
	"4B3nMPnk2q7PVzCVkpXvKjxdKGzKpLqB3a7MCwEnPyDaAsVG+0s7JnYkyrIL7LodLdAdDlEcmMgt1uLJ",
	"pAiCNIlblESWTwSnSG3XeqBt4HlVmIKkHOOUQpjWt5dxLQYAd8YIQLQEI5JMwRSOcQAiHN+2vpzk+DbJ",
	"ohwjYrmJQsczpsB5bUykY3qaNI51WRxPEWVwmnZ4Hda38zFNCLtA8r8OaPMxrXEqVu8eLgQnXk1HOGJK",
	"fipPaD5XyxdtK73WwjxKOkr/AJKKZrM6rNT4LLU2Gzr0EIYSiN6JmyRAERd8w5H1piACgfqRovtqOcIQ",
	"GrQ5z3Ad7vdgLiRftT848ony1V610pXrtITJfRwlMHSpoZVhrwvPkwvs7FeuQaZMRfy74H2hlBJSZQAc",
	"QRyh0A6mGgxOK76IHSxcQulxDNOjNI3KZvGOzkdNprJ+L8bjCWubhr+TCA5RB4PbKULHhZnvnCD+znKy",
	"l8XfiP0eDVAMCU46x4Zdqo7Wc1lSopkzmhNe+W/cRf/dV6+XfYFoFnnK96cJGSesVQ+cu2jkx0b+0hpO",
	"KFpdeUzc6UmhuwGCKGIAx5SRTMqQgPI3BkvALMkIcK+ytqBvIQnvIUGGdO8AxvOzunTxfBDiMWagrGsp",
	"ZMkUMoYIb/knH/YHv7z69PzhFzauc3xgkeUxm4E7GGUI7P0apjBGFAGp3QERvkXg7/7m8e/+z98KPXmL",
	"Pulby/AHIEJ8ceBbpRYCCQGQTGcDguQLQn3fe9X/tv9d/03/Xf+H6mTlHc4f/8388d/OH//d/PFv54//",
	"fv74H+aP/3H++J/mj/95/vhf5o//df743+aP/33++D/mj/9z/vi/5o//e/74f+eP/2/++P/nn//Z/PPj",
	"/PPn+ec/m3/+8/nnfz7//C/mn/9y/vmv5o+f549/Nn/88/njX8wf/3L++Ffzx7+eP/6r+ePfzD//6/nn",
	"v55//pfzz3/x6tvv3rz74coK39cndQC8ULiTurAGnL2w40yon3Jhujx2Tk2gaAVokBDjjjQ0OVP4EU/5",
	"bXMgbB7y732LhgdP4Ripq7E842/EHzACogl4f3EmJdxQr6TBVIEtBpo/UtDRL7koYYDvg+ERtoPpj+xg",
	"Ki7U8gT7h0DYHFDIjy2fBuyJ1/KTPjg4BBEaMcAmqLSAPf5sfCIsFOJu3u8fXNnejswy3zstG4NkVILK",
	"AlYUISIdH/T6nCmI4yVIzARovnEbFxQuaU5GY4C5tov3yj2MH1bB5UChS7eg12V+48M0GO8q263gvcGk",
	"VoTnGgKU1IsKMuc4iiE/MsoIFFpl1zeS+3tdD+WN6S5At2h+oVe8Ynwe7G+VgGRzPBA/Wg5wKYghLF9q",
	"+98c7u+Xz9Peh/3BwdU/EsfqT5992B88v3py+GF/8IdX4qdfOA2D5ZGfPVvByBVSkNPIbfTFjm1kUPV2",
	"2lx82WXZS9awrnlotTqYbDv533DZpMtuuymcGGYRarBvNkuPJqq0oWrT7g61NRjMg6MGhcXj9Np0deDv",
	"EMrg7Poeklg+0Lhsjq5hhASZ3kjj/jVBUxyHJVtXsfbzkq/MglbsJY9vq8178bwNR3cQR/AGR5jN/J2B",
	"Sr0sNvcy230jBRggWgHhKNngEhpiyqBdblJfhL4YMUSUupgiSIIJl/uziAmFsYc5bCVOABVvVYIKiUQp",
	"tK1b7OwpULnGWiDoyw+5LNhx/zvuoLDkOWt1Z1goDUmTB8QC7NTGIx0ns86w5NcINSxIBForg5M3N1nY",
	"IN0p/UotTU+Lt8FK+KTs8nLmdaY2mqZpRZH13eWbJfRvKyb3DWTgKbvAri3rTlXNaHE13ECAszu4yOKo",
	"VazImOrKurfcWusXSCI11M2BI0t4+S8qLXkGBowgjjKCLtyWUN9z2xphsGBowHLO/gQxMjt22xA8sygZ",
	"mVJ2LLtPqeUZjm8b2126zdqbfivpMOJmm7PbfCy+HCuTtH5k4ZhmoxEOMH9gyYRb8oRdhyiIcKxcV4TJ",
	"65r/Lv1U9JMsjxFSAS/8xSVXcGU9O1F0A4NbqbG0CNlHWoQBuilIZFup957COBM+JtKxSFnv/SXcJk2A",
	"IHw+t/0itamORjCinrojg94Wi3xawPqZepyGtFiXnSM0bKYIMasYMzXrrnookRCIb2Dve0xhH7yBlCHC",
	"qaoPEAuePllV6A2H1exNErOJYxmihbTYTHkzsHcwOHhmd0eSo/2AIGkfbMZbuZ9tFuHtRBOTKwSVTTAF",
	"mAqNuHeInpT0X9RH5RcneAGEhp+CZAQ49Js8oExu2eiSp1VO5fnOyz6ATPoK5P4MkqGkcJbCmZVlbInJ",
	"iujb72GEQ9ji92fN72DHJG870KjDFCDZAYwSIrArYnttuJxA6g7utNOMHA1MIAXQQTaAoggFzBHLPIF0",
	"ocniWWUaCmAYOibBVAD6WCn2vDd0xzFT3Q4Howliap0yRh/ZJUOpLTDpIwMEBcl0iuIQhYAylAKW6Dhc",
	"JKPADfKFYVi/CSli1wrg9Y8S5dd8mfpjTxP5de2ajZHDv0dI6+LdZ3PErAEtRiikfCcM3iLle8n/KVdT",
	"Alqv03VWt/ikKA61OlZLBJUoNeU2ozN8OgxB5zq6sC22o8KQbeZ4sHfw7HnFgP18NVeNGTpSXkluXM8D",
	"28De/PFz3Y6+pod/kxvsaRZFYJz7wra5wS4b9FKevOrRsOfvzVCNjqkGzxvmfIHzF5VBX+zCDWMCUx+b",
	"mI8TyYMyRSTA4h8ExUz8McURZpDYr8hzggP0G+WXZY1iXEXqHmFcSRG5zmKpeWk2duZGSU+IxphdTwvd",
	"ez74Qd8zsKxsniyNV1u9VY1R6LJsPq+zsk5J4+2N8CN4J9wqfyuY2rtJ1uv3Tgnu9XuXkK/oMrM79Fcf",
	"JwpRHdTcf/onz14c7u//wqXHWwhhS6rZl8CkgPJKMUltqMTR7NrLYI9jzDCMrkcEIeuWrL2ihF07pH3h",
	"Ycnn9ldQ5j4HNvWkOvMd1J0lVmHLn6WB5j2cPjNtnpMKLA6o9g286FXYcHthZNhxe6ijOxyoQAOHq5Rs",
	"Yrg1AWlrFJoqwAgMbqXgYhG+GnP81DykjdaODRlZy3cl6bfOXuJpGFkmLEQCwDsqRDZfMHnMGrOkd3EK",
	"8QJXXaAWGigUNrueLwzWMaasISmktz9wIUNYRE51VxsS2TN7oLDTfS13BJ5mlIEbBCADEYKUlTJItCWQ",
	"MFIRVobnPxeScGmh1oOCRogQFJ7BeJzlbmnahPAj7FXzLZ3rHiBSXUCQhAjs/QgFC9LCcR+gWPzwKh5H",
	"mE4M10c5LLILFeKJ2i0FVP5cTFSas8q7Osz44EJap4w0pqAidty/j/FPmXyLCg9Gvq8oGeO4DOI/3C+h",
	"7XnZ0xQOfnc0+Kf7g19eD67+Ybu7Wr6afk6phtdjrEPHq/iz82iK4tArFC0/JrZQNOVRxAFNxJDV4DTT",
	"uWVZd3u+6HY3/9QrTUv7cXKE4IlFqKg77aavso357U+O2+J6WtnqMoEFRWyynXGHIn5YacqLUYaamr1Y",
	"rYjDashFoJIFqIbniFwWNrf6rabiul7OlBebIww98vYAiJqyVyMS8Efp2NcPpCF63SfKPt/cOSJY8jLH",
	"/tK8wSYj6PPBf0WSe2ltWEG2Btu8lVifOtHIqK2LmgDf+u68wZGwuTXmv7lZNI/+yJm3sfC206UfLDFp",
	"jblnGlZsBWFxjFbmqbBAwnsdgHBciiLxIBufED//dPqvjYASS/K67v4ohCTkTYOBFX3ErHnSbaTslyZl",
	"U33W5ES17pR6ay4e0THf2EKFCDasTs0T9juD8M0jWpN8Y2El0Q//e0hBkbHD9zy7tA0nVSWDu/tbq9z8",
	"bTaF8YAgGAo7jVJaKNnVMdI7qxlWLYX3AXvT5AZHqA9CRG9ZkvYB48OzJ8ucsPSocJevFA09zwXfZGRC",
	"235SVX5BP3u48nOvj2ipLWQjAGEN54iVo5pkkFFvGmggy4aQKNMkZiZ0dLvPtBfhoAsWtDLzp7SHENtS",
	"U1sMdVjY0QOCLIC/FEoKnVZbDAVkD0BlF6ufnply3zqgbgJETF79hkknaIoIjL5DM8srTX8Ft2gmHqny",
	"pIDLk+/sUXbZDX+b89NjHVAtymjGR/Z4WVSezxdnYjW6ERhFyT3AI2HXRY64oxxFDdAqgd8GMSv2pV+c",
	"sNsrJy13PGPZvaXsNlU13jEAaRcPF0ueKDv4y+oMO2WsDmClmLnSqPUl216z0j3/92k2f59mc11xKQ9u",
	"qtu5rI+OhS5QwKjC68WHYYhp7gIjUvnn3KLkvlInpdDij9WFB+XeSqVZaWO1lCad4t49jiJdnQIQNCip",
	"FvEIBCL/c/jEQ8e4EhW+qFgwe+VYexn6MpzdrHHg8N0SY55ndNI6ZJrRiceIazUDbFLvv33le/2kUjj2",
	"UHN65US112f3zvvJ74xFFXxapu6u4WsrJArH6OXsBM4a9hTCmf3SWCAkb1lNp1pws5pZIbUZm6vRRW8e",
	"BnYql65ofldQgxnuVdx6E4EkzkOQRbLS/moqyW3jMmu/hHakFpj7Dmy789ousEWvoxXeP563y86U1Vuy",
	"4F3jzeh7q6ltGjod5bgPwym2w8/YqU0VRDOaCpdz/kRU/h62YXwCEoT6qOOzTLjT2ylSpdfoNGAdbILz",
	"BhnBbHbJkS+X+hJBgshRJm/VG/GvU43TX//2nUiwx1vzJYmvBYomjKVmEjc9CuaInSAYisYS771/MtDN",
	"Bu+UQVmvOMXfIb5k4e43StTzhr+ODXN+j2ZpmhD2j+k0SdjkKY6DYvBL8Rt4HQeSlZYY5hSSIpHWFMZw",
	"jGQ9uRllaAruMZsYjDPCAYopUv7YuaGo/8dx/S7oi3pkBMFIUL5lkqd/zDeqxuS7UQs+SmEwQeDZ0/1e",
	"v5eRSEGTHg6H9/f3T6H4/DQh46HqS4dnr49fvb18NXj2dP/phE0jI/mM3r9SWIA3xSaPzl/3+r07RKjK",
	"O/d0/+m+fg/DFPcOe8+f7j99LhMVTQRNDMUhGuZJSIehTt7Pv46lLlE+mVVMae9XiIk6g7momef7l2m5",
	"oEwnIuqIC/L4KUMi37eCh7IcK2KDZRcelnARrOCS+t/3CN1yAVrEmPV7P2WQMEFyIgLMlhLUMXmlFku+",
	"hDZTyFW/RxQTEIB7tr+viVepz4VFWD5+hj8qh7Ni/CbGbCmYIE5IxaagW4EcW4AgRjC6q2Z5f+j3Xuw/",
	"d02b72N4mpAbHIZIRp1QXXKUoxiE9ek4WCDnTR9kNUwwAMWKr/gQNXIyfAH8iOkiT5u7LCkJpZpBSvrf",
	"nJTEH4KYxF+ciKJZBzLKkzJbSchV5cA91rtkFSMtTNyO8cYkydKXMyd8mSnPRAnr9fWP4oa+2uwpqjn6",
	"WM6QarOZE0Sqky1wfjJt3vc7Pe9VJr6NnZ2f7ZFZJyVXNDkWOhYtNkPFWXkqbxpWkB3o/DCNBGyWdvGi",
	"XpmQxkq8z/bNNK/7+7Y4GPugKv+MddQOw6jXWN8T24YlgY9YNdOJdGw3M6BzkRZZSK1zi/aluTdJuVVk",
	"FobuOg2fF+lm10rAMIrM1LYU7AkCfWKhZL2ks4QZgnXvir/5E2qh3/OE2giYSFPJyyScrQy0rnKAD2Uj",
	"KCMZeqhh+GBlyzA9rhpxqh17LAjdb0foS5g7US9LAxJwIBYO1vnyumDfztSGn4Re9UGyCR1PX6aPE/F7",
	"lUL0bWPjc/xlZrA51bKM3+XuqRe2KI8CbXIry59D3uNFe4+3CTtNsjisIE0CblGE9b1vm+2iYm2MVwe4",
	"tRxRVRdnhdx3Oaxzdp1a1rcYx85sDDvbPgms/m5w+bB43Q37W7gbrLXpNnM3LEehEtLruUiGynNjIB0+",
	"OknNgohL7iR0QyTtEIJxHERZiF7Hhda9LlS7Enotyyk75qJUCVDrivU6DUsIy1oTY2V03V3+mVqX20Cx",
	"x2bDjnLv9kjwaq0St81Ha2tCtyZVT9LcSSHcttAONNmNiQ4/yT8WF9TLJH2sRtswdy2PGxSLWPfLwEpV",
	"u/VGWJKe+stcsl8zOezvAP/apZuVpijAIxysgOI6vU2+aopb22No4Wt7F8j+i38fbeuOH4rnhk5luIzs",
	"qs/ZkR7w9+fNoXpSAPqyT5ymmy/wzGkErPTUKZfSdhVE7nv687LaVTLiu0ZVsdYrUKgUaf1XMNhuGuOn",
	"OFa5Fiz2y1YETuFHn94b0MLLI9Fs+5Rt1m/3VPM0adClq6NTUSlHGH7Kk649eHOF8zxPm8/lmRqtd9rK",
	"IpbZaGKRIN1N80p5bSsljKFMMe0le1UoRCbQ2zSdLCYNdcpmI7KP8b2pvDZgD49ACgnDMLIXQCjyFVad",
	"1/jvKjRdQastHaYYqR72vFmprJJE0uqXJyCUJ8L88oSwc7l0AzMdj1MEVZR2M2eVzb4oaasS0YMjhgi4",
	"mclc+SKqf3WSk8t7qpJy3eoKaGRwb/KfcnkS5nnGvUXIInu704qmc85bFrQ6c1n7lSeorukAn5mhFGsX",
	"a6LybO47TC9LbKD56A0/qWxN3nYC0e1cpXjyurjytmvW2p9VAlt2SFtfjrlZCHN9Hxa5fbys4ww2CJ1V",
	"nO+e6BlZV7jiw9tJAWhSSic9365TTLWsfyu9NKm/tqLLKhHLyogjRIuSx0nR82dJIAXktn+RwNUSScWY",
	"EMAoyHiHQUrQHUb3PqRSNh3oEc7VAOtRvJ8ipGfCSazm2pLi3bEWN3WdIgSCogfQsK6+qsSvYFRpzd/A",
	"IR6NkExHqHL10sV03RX838mob9QZ7d/rjutBdyVX00bRa4mEt+C0aAWITJtcxqYG0AoNFQSlCWF0iD7y",
	"/3ug7EJ2eCXbrwdTcnA505bOY3kJTYoX3sKoTbZtRzG5cBBCBoFErnfEmZlspvGFkCcK+rkZrSrZg9dv",
	"tFp1RPKSOprdNHupdNnbVfHUkt02ePDrg7apqL18PvdbUS3fJePpEYafcjPqgzebuDQsr+2Sv2mn3WF1",
	"gk4D1x4opLM073KwUHWNHQmlxSFv64SwBqcdvi/prJXnpN6KoKKLRHgQ4NcSNKT3s1JuNpQ5zT0k4Bo1",
	"H8ueXwRNl02vHpZSleq9UkZrt+2lHc5Ensn+yzsVkuyqp2JVp8Eocd75POi+X+CJcDkj6IIruRYFhQBq",
	"bylLxZrOpWE8jqIs/aLLu7vOY+3k7cpJ0/UjvryT9kYAPpoVlfWXPnT8idj+0H5PZXHIn9cr28yz6RpS",
	"ZPjsMqBM9dnk4qDzavZlfu8+SIjOnLpgphjH2suJYrf7bBUU1vRmFQ3W/VAVp6FBnuOLaDxJw09S5/Lg",
	"d6TeawVN+/2U63J2+C3Kt9PwEBXQ28XXZ2YurAv2W96b28LxWp+ZZgmJDcvTIme4i7C+9FelIMJVsJ4u",
	"nhMGhXbym9hxbuRhFRfw3DlvCZURewnkq5zY3XB/qTrtPpPqrDeQ8KA78UrxJcs8r/mWyVKRRUeqzNhk",
	"KKvmDMwy2Q3UmLHJsehwXhTOXksikNIkW7rEPIggL66tig+t5k47aO/yPubYSwj+HappecRS8tLmunTF",
	"BMWMwwHVqaT4aBj7OXGMEjJOWAfiOBUd1kwc5Um25X5TWYQHjcgC7LIUlKgVvzSxGFUHeocfrkwqUG0K",
	"MhCze2Fd1k5qxfWZKrG0DhSLsbeEWb63Ri89vjazjv7iZ9aBOzmD9yklaESQLLjSjLEL1XA9OFOji/oT",
	"O4o6sTag4GXl1suxXgUCAMWwgOlSHB4oHGPKVJ2lFhyqlutCohx+S2m32hAoBBkNrFVdt79s73KcxKMI",
	"B20sV64LQJF1q8P5pSgOS/UMfeiAd/re7LMuiqhOtLvimLnKzV20HD5qtlJNygjHt94E0EXK4lOuW8gq",
	"zbEljFfW4C1iVe/mTYngDRRiCGKZKHTnfzd4OVlmbGL4WK7beteo/7/08VVb7p7VquA/oFIxlFfcp50g",
	"yiXdJGMDGEXth05v60z0OYqi3nZ53VEUgYRNECmc9RgiUxw7tGTLQVzuGoxIMhX2l3xO9DFAKQNBRgiK",
	"WTfwV1wDWyJADRx8bf6BHujW1uHV4ng5rZNJFHmqPouB20kJ4rqcDfKSmU3sTdztqvRkDeu2wtGlu1iw",
	"W7nSKRyLSnTicrZZWzVrdtPKRu11LSKxudmtKqAct5/Em5KPdDFveQcWmPC7Dm+S5FaXanSRykvdxssH",
	"oqOfgRq81Zs/DZKprKu+aym41Q58Mm9rSDrvcdeVfFOgQKMzx0pzgmsDeetLL60m2dILN0eAE+CrzSDd",
	"+XFbTSAtH7MKp3aUmmdz+En95XWp60Fe6j5eF/qN0Xo3L3QfLK/Oj9Tm5AmbUdZvZaA/Q5T4eLws579y",
	"U57JfZwmqpzvMK+R2/xC0eV/T/Lm62GhtXm2pBow5neLRnmjVaa5Kos6nyr1mT9cPZRV+faCx1IS1UgG",
	"eyhmZDZEHzEzPUr00IomRG11Ba5GGehtqaGXIJTFBMGwm8DSX8ivdOXpIb3kHhMkPsJPCYSdJaC4ggCN",
	"0DJiLFgdfjL/+Tp8GE4huR0I3PDzD1kwsTAA/nNp7LelUd5Acnsh0dvOycsL+JKfzCYQAIcjCgHkyITh",
	"8v7d5LaE5XzgZmT7luhsrM5ZMUdChlkWImHnjxI53+AGCmanPY6t5xQyRyxtkt1ERsiByuFmcYU+S+Lx",
	"gpNXXkcLTK78sAkMcUYBjoECkX1C2czOlg72u7i4Uxkhby3iiynj4pdZx7f4KSU4QI76va6MfSMEWUaQ",
	"a1fGZwtDrIWMVLhef4cDAzZTHGP5cqllNyxJkmbtU4MhGEXKbPzArG7pwRe+ljqK/iXs1i+eWyohdsff",
	"EN5BHMEbHAkR0R+ZR2a/rwmxpY3ZTBnGd4BjueimWjSWwu8wEoFyAJaB2Ig7kR52MEVskoQt17Fo+ka1",
	"3EytPGNKr0J5Ktut2k5nmZVC3jYtj1KCoMqmq740a/JWC7FmkyTL0tdioGb7CctSgEW71WnYbAqzS0Zw",
	"igA1JxzJMuKcAqGqi2zCuRHMFlodUjHHIIBRdAOD22bdQBkXcnnHuud61ARyEgFzPdPW6pOUzlHbuVFo",
	"W2HoaYlEvoVxGFVIpIjOBUGBlW70oDOoDcSQ/qxM5wcTmOqtHw1iHr/UZnrjEkh3eR9XhjM8koE5E0gr",
	"p4sPAbJUO0Ing7QoodINzDrxvmzlpeEuw/u83L9L8n2jzzKGyRebdY8v4YGgabJqse1CjAmg9f5aHsFD",
	"itggf6j4c9kKoi8RO1GDbB7lq9S6ij2UNtesgBXtq+fRHQC4RAQMqk0DKVCYy48+lysD0PX8+8iGqwm6",
	"39XXdBeRtYuwOsGUJWTWWVhNy/3riKxicHiPbiZJcqtFJ6+zTH+rOsn7eglZaavJbTwYtdqo007S4N+h",
	"ZBkFX4DiME1wyRPMhZJS9hghO+ubeSC8c7xQVOSQkbWj5c9n0rvna/ANM3bkc9UKb5pNvG9Sc0IRv6mT",
	"bKmsM6rBk46UoEhwYEpqHUlBVYY5yliiedKXnz6u2Mz2gmuKFTT4pVav2VXXGHq26qP1ipCE+JytEcRR",
	"3dutzQSs6xTV5A+ZLa14adYcJyvHxccRvFui3fUks/2aZZw8fZWPjOOZu9Ut5JQHMCgjR3OZMmRCCeRD",
	"IHmVo52CmVxVPQ1tV9BB+zgeEPRMV/u1eqJ3SM+2OXNMZUY3FmWGjfbCbiKhRl7YbQMvI1Etw4P+PYt6",
	"uci+XKXLvEnkD806+ypU1uWAK6bYkvutwkRrcZ4w3L4D7lEYKmuBq+pKjtYq8XcprWagfUdKq22n3s5a",
	"tZNdMEiSEY5QO/9S7baVxkutc438PzOmMWAmc1w25XSrwWf1rGxnU6ypba8uy5ozBZobM7UX0ksECSKl",
	"9xEid3aHtnOShJn0kpWNev1eRqLeYW/CWEoPh0OY4qd0miRs8hTHwfDuoGfxDmNwLGUH6xBUfh74DHWC",
	"7lCUpMoaVRvucDiMkgBGk4Syw2/2v9nn6xMjXeWQ+WTNaVaKcQIwDoGOqdIB/YrXVqKh6ksU403zBE8g",
	"PxG0GEQix+LEV+KCxSBFT8Wk6l1N7xzTb4NvJXf/04OYHhjuobSsZ1tHLu3Vu+snuDC4j4nKAKB0kQYQ",
	"cpdq2wpK6gphq49DQ8Wcb0S9yt2bIIjTSRUBuYu/A39Vr2HVrexHWu8rk3zlexVaBdORyVr6yQrf5iJQ",
	"3Wc+vnh/YpCiAKfPvGdJqWiz38SCJYlEe5gyUtBh04TVvGh+M0UVUdGcsj6HsyJeN3jqc2HZYcIZKR5P",
	"LBu05JT2nbZ6GPqq0Dbti0lHOIZxgGFk5TUNJbj95oe63lRf1aQSK+AT32QUx4hScdKjCI+R9K6tzFwU",
	"rHq4evj7AAAA//+4AabW8jUBAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}

package app

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/configs"
	"github.com/smooth-inc/backend/internal/container"
	"github.com/smooth-inc/backend/internal/infra/database/postgres"
	"github.com/smooth-inc/backend/internal/infra/database/schema"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/server"
)

type App struct {
	config    *configs.Config
	logger    *logger.Logger
	db        *gorm.DB
	server    *http.Server
	container *container.Container
}

func New() *App {
	return &App{}
}

func (a *App) Initialize() error {
	if err := a.loadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	if err := a.initLogger(); err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}

	if err := a.initDatabase(); err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	if err := a.setupDatabaseSchema(); err != nil {
		return fmt.Errorf("failed to setup database schema: %w", err)
	}

	a.initContainer()

	if err := a.setupServer(); err != nil {
		return fmt.Errorf("failed to setup server: %w", err)
	}

	a.logger.LogInfo(context.Background(), "Application initialized successfully")
	return nil
}

func (a *App) loadConfig() error {
	a.config = configs.Load()
	return nil
}

func (a *App) initLogger() error {
	loggerConfig := logger.Config{
		Level:       getLogLevel(a.config.Environment),
		Format:      getLogFormat(a.config.Environment),
		ServiceName: "smooth-backend",
		Version:     "1.0.0",
	}
	a.logger = logger.New(loggerConfig)

	a.logger.LogInfo(context.Background(), "Application initializing", map[string]interface{}{
		"environment": a.config.Environment,
		"version":     "1.0.0",
	})
	return nil
}

func (a *App) initDatabase() error {
	dbConfig := postgres.Config{
		Host:     a.config.Database.Host,
		Port:     a.config.Database.Port,
		User:     a.config.Database.User,
		Password: a.config.Database.Password,
		DBName:   a.config.Database.DBName,
		SSLMode:  a.config.Database.SSLMode,
		TimeZone: a.config.Database.TimeZone,
	}

	db, err := postgres.NewConnection(dbConfig)
	if err != nil {
		return err
	}

	a.db = db
	a.logger.LogInfo(context.Background(), "Database connection established")
	return nil
}

func (a *App) setupDatabaseSchema() error {
	a.logger.LogInfo(context.Background(), "Setting up database schema...")

	schemaManager := schema.NewManager(a.db)
	if err := schemaManager.SetupDatabase(context.Background()); err != nil {
		return err
	}

	a.logger.LogInfo(context.Background(), "Database schema setup completed")
	return nil
}

func (a *App) initContainer() {
	a.container = container.New(a.config, a.logger, a.db)
}

func (a *App) setupServer() error {
	if a.config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	serverConfig := server.Config{
		Port:         a.config.Server.Port,
		ReadTimeout:  time.Duration(a.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(a.config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(a.config.Server.IdleTimeout) * time.Second,
		Environment:  a.config.Environment,
	}

	srv := server.New(serverConfig, a.container.GetController(), a.container.GetLogger(), a.container.GetJWTService())
	a.server = srv.GetHTTPServer()

	return nil
}

func (a *App) Start() error {
	go func() {
		a.logger.LogInfo(context.Background(), "Starting HTTP server", map[string]interface{}{
			"port": a.config.Server.Port,
			"addr": a.server.Addr,
		})

		if err := a.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			a.logger.LogError(context.Background(), err, "Failed to start server")
			os.Exit(1)
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	a.logger.LogInfo(context.Background(), "Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := a.server.Shutdown(ctx); err != nil {
		a.logger.LogError(context.Background(), err, "Server forced to shutdown")
		return err
	}

	a.logger.LogInfo(context.Background(), "Server exited")
	return nil
}

func (a *App) Shutdown() error {
	a.logger.LogInfo(context.Background(), "Starting application shutdown...")

	if a.container != nil {
		if err := a.container.Shutdown(context.Background()); err != nil {
			a.logger.LogError(context.Background(), err, "Failed to shutdown container")
		}
	}

	a.logger.LogInfo(context.Background(), "Application shutdown completed")
	return nil
}

func getLogLevel(environment string) string {
	switch environment {
	case "production":
		return "info"
	case "staging":
		return "info"
	default:
		return "debug"
	}
}

func getLogFormat(environment string) string {
	if environment == "production" || environment == "staging" {
		return "json"
	}
	return "text"
}

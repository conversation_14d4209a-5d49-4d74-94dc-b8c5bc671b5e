package repository

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/smooth-inc/backend/internal/domain"
)

// Stub implementations for all repositories



func NewBookingRepository(db *gorm.DB) BookingRepository {
	return &bookingRepository{db: db}
}

func NewNotificationRepository(db *gorm.DB) NotificationRepository {
	return &notificationRepository{db: db}
}

// Basic struct definitions
type bookingRepository struct{ db *gorm.DB }
type notificationRepository struct{ db *gorm.DB }



func (r *bookingRepository) Create(ctx context.Context, booking *domain.Booking) error { return nil }
func (r *bookingRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error) {
	return nil, nil
}
func (r *bookingRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error) {
	return nil, nil
}
func (r *bookingRepository) Update(ctx context.Context, booking *domain.Booking) error { return nil }
func (r *bookingRepository) Delete(ctx context.Context, id uuid.UUID) error            { return nil }
func (r *bookingRepository) CheckConflict(ctx context.Context, parkingLotID uuid.UUID, startTime, endTime string) (bool, error) {
	return false, nil
}

func (r *notificationRepository) Create(ctx context.Context, notification *domain.Notification) error {
	return nil
}
func (r *notificationRepository) GetByID(ctx context.Context, id uuid.UUID) (*domain.Notification, error) {
	return nil, nil
}
func (r *notificationRepository) GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error) {
	return nil, nil
}
func (r *notificationRepository) Update(ctx context.Context, notification *domain.Notification) error {
	return nil
}
func (r *notificationRepository) Delete(ctx context.Context, id uuid.UUID) error     { return nil }
func (r *notificationRepository) MarkAsRead(ctx context.Context, id uuid.UUID) error { return nil }
